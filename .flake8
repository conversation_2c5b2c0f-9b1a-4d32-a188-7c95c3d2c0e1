[flake8]
select = B,C,E,F,P,T4,W,B9,N,C4,T0,A0,N4,CF,R,SIM,H,VNE,ECE,CCR,A5,AAA,ANN
ignore =
    W503,W504
    H238 ; old style class declaration, use new style (inherit from `object`)
    H301 ; Do not import more than one module per line (*)
    H304 ; No relative imports. is a relative import
    H306 ; imports not in alphabetical order
    H401 ; docstring should not start with a space
    H404 ; multi line docstring should start without a leading new line
    H405 ; Multi line docstrings should start with a one line summary followed by an empty line
    Q000 ; Double quotes found but single quotes preferred
    AAA01 ; no Act block found in test
    ANN101 ; ANN101 Missing type annotation for self in method
    ANN204 ; return type annotation for special method
    ANN102 ; Missing type annotation for cls in classmethod
    VNE003
    A003
    ECE001
    ANN003
    ANN201
    B907
    E128
    E800


exclude = env/*, scripts/*, tests/*
max-line-length = 120
max-complexity = 12
max-expression-complexity = 6
max-cognitive-complexity = 30