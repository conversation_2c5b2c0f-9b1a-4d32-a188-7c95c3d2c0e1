# backend 项目模板

```
|-- backend                 # 核心代码目录
 |--- executors             # executor代码
|-- conf                    # 配置文件目录
|-- scripts                 # python脚本：数据处理、转化等
 |--- async_client.py       # client异步调用示例
 |--- flow.py               # flow启动脚本
 |--- sync_client.py        # client同步调用示例
|-- tests                   # 单元测试文件目录
|-- .gitignore              # git忽略文件，例如*.pyc
|-- .flake8                 # 代码风格检查配置
|-- mypy.ini                # 代码静态检查配置
|-- pytest.ini              # 单元测试配置
|-- .coveragerc             # 单元测试覆盖率配置
|-- requirements.txt        # 运行时依赖包说明文件
```

## 0. 安装

需要python 3.8版本

```
$ git clone https://gitlab.yunfutech.com/yfstarter/flowstarter.git
$ cd flowstarter
$ pip install -r requirements.txt
```

## 1. 使用方式

- fork 项目
- 移除如下示例文件

```
backend/predictors/ner_predictor.py
backend/executors/example_executor.py
tests/backend/executors/test_example_executor.py
```
- 修改deployment.yml中的资源限制

```
#模型服务配置：
            limits:
              cpu: 1000m
              memory: 2000Mi
#非模型服务配置：
            limits:
              cpu: 300m
              memory: 400Mi
```
- client端示例
```
scripts/client.py
```
## 2. 单元测试

```
pytest
```

## 3. 本地启动

```
python -m scripts.flow -p 8000 -m 8080

# 测试服务是否启动
curl localhost:8080/metrics
```
