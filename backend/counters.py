from typing import Dict

from yfflow import YfLogger
from yunfu.common import ConfigUtils

from backend.graph import get_graph_mapper
from backend.models import BaseCount, Kg, KgVersion
from backend.utils.utils import GraphMapperUtil

logger = YfLogger(__name__)


class Counter:
    def __init__(self) -> None:
        self.conf = ConfigUtils.load("conf/config.yaml")
        self.kind_modes = {
            "relation": "relations",
            "entity": "entities",
            "property": "properties",
        }

    def count(self, kg_id: int, version_number: int, type_: int) -> None:
        kg = Kg.objects.get(id=kg_id)
        space = GraphMapperUtil.get_label(kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        if type_ == "kg":
            origin_data, total_count = graph_mapper.count_kg(space)
        else:
            origin_data, total_count = graph_mapper.count_ontology(space)
        data = self._filter_data(origin_data)
        for kind, mode in self.kind_modes.items():
            self._update_count(
                kg_id, data[mode], total_count[mode], mode, kind, version_number
            )
        logger.info("[Count Data] 完成")

    def _update_count(
        self,
        kg_id: int,
        data: dict,
        total: int,
        mode: str,
        kind: str,
        version_number: int,
    ) -> None:
        """更新图谱的关系类型或实体类型数据"""
        kg_count = KgVersion.objects.filter(kg__id=kg_id, number=version_number).first()
        if not kg_count:
            return
        if data:
            for key, value in data.items():
                try:
                    base_count = getattr(kg_count, mode).get(name=key, kg__id=kg_id)
                except BaseCount.DoesNotExist:
                    base_count = BaseCount(name=key, kg_id=kg_id)
                base_count.count = value
                base_count.save()
                # kg_count.relations.add(base_count)
                getattr(kg_count, mode).add(base_count)
        setattr(kg_count, f"{kind}_count", total)
        kg_count.save(
            update_fields=["entity_count", "property_count", "relation_count"]
        )
        search_params = {f"kg_count_{mode}__kg__id": kg_id}
        base_counts = BaseCount.objects.filter(**search_params)
        for base_count in base_counts:
            if base_count.name not in data:
                base_count.delete()

    def _filter_data(self, data: dict) -> Dict[str, dict]:
        results: Dict[str, dict] = {"entities": {}, "relations": {}}
        for item in results:
            for key, value in data[item].items():
                if not key:
                    continue
                if key == "概念":
                    continue
                key_ = "其他" if key == "null" else key
                results[item][key_] = value
        results["properties"] = data.get("properties", {})
        return results
