import json
from typing import Dict

from asgiref.sync import sync_to_async
from backend.mappers import GraphMapper
from backend.models import KgOntologies
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.utils import Graph<PERSON>apperUtil
from docarray import DocumentArray
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils

conf = ConfigUtils.load('conf/config.yaml')


class OntologiesExecutor(YfExecutor):

    graph_mapper = GraphMapper(conf.get('NEO4J_CONFIGS'))

    @requests(on='/calc_ontologies')
    async def count(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags['kg_id']
            self.logger.info(f'{kg_id=}')
            await sync_to_async(self.process)(int(kg_id))
            self.logger.info('计算完成！')

    @close_old_database_connections
    def process(self, kg_id: int):
        label = GraphMapperUtil.get_label(kg_id)
        self.logger.info('开始计算！')
        ontologies = self.graph_mapper.get_ontologies(label)
        KgOntologies.objects.update_or_create(
            defaults={'ontologies': json.dumps(ontologies, ensure_ascii=False)}, kg_id=kg_id)
        self.logger.info(f'ontologies: {ontologies}')

    @requests(on='/get_ontologies')
    async def get(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags['kg_id']
            self.logger.info(f'{kg_id=}')
            ontologies = await sync_to_async(self.get_relation_types)(int(kg_id))
            doc.tags['data'] = ontologies
            self.logger.info('统计完成！')

    @close_old_database_connections
    def get_relation_types(self, kg_id: int):
        label = GraphMapperUtil.get_label(kg_id)
        self.logger.info('开始统计！')
        record = KgOntologies.objects.filter(kg_id=kg_id).first()
        if record:
            ontologies = json.loads(record.ontologies)
        else:
            ontologies = self.graph_mapper.get_ontologies(label)
        self.logger.info(f'ontologies: {ontologies}')
        return ontologies
