import json
from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils

from backend.graph import get_graph_mapper
from backend.models import Kg, KgRelationTypes
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.utils import GraphMapperUtil

conf = ConfigUtils.load('conf/config.yaml')


class RelationTypesExecutor(YfExecutor):

    @requests(on='/calc_relation_types')
    async def count(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags['kg_id']
            self.logger.info(f'{kg_id=}')
            await sync_to_async(self.process)(int(kg_id))
            self.logger.info('计算完成！')

    @close_old_database_connections
    def process(self, kg_id: int):
        label = GraphMapperUtil.get_label(kg_id)
        self.logger.info('开始计算！')
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        relation_types = graph_mapper.get_relation_types(label)
        KgRelationTypes.objects.update_or_create(
            defaults={'relation_types': json.dumps(relation_types, ensure_ascii=False)}, kg_id=kg_id)
        self.logger.info(f'relation_types: {relation_types}')

    @requests(on='/get_relation_types')
    async def get(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        """图谱中实体、关系、属性数量统计"""
        for doc in docs:
            kg_id = doc.tags['kg_id']
            eid = doc.tags['eid']
            self.logger.info(f'{kg_id=}')
            relation_types = await sync_to_async(self.get_relation_types)(int(kg_id), eid)
            doc.tags['data'] = relation_types
            self.logger.info('统计完成！')

    @close_old_database_connections
    def get_relation_types(self, kg_id: int, eid: str):
        label = GraphMapperUtil.get_label(kg_id)
        self.logger.info('开始统计！')
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        if eid:
            self.logger.info(f'eid is {eid}')
            relation_types = graph_mapper.get_relation_types(label, eid)
        else:
            record = KgRelationTypes.objects.filter(kg_id=kg_id).first()
            if record:
                relation_types = json.loads(record.relation_types)
            else:
                relation_types = graph_mapper.get_relation_types(label)
        self.logger.info(f'relation_types: {relation_types}')
        return relation_types
