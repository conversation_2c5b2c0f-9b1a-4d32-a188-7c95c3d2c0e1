from typing import Dict

from asgiref.sync import sync_to_async
from backend.mappers import GraphMapper
from backend.models import Kg
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.utils import GraphMapperUtil
from docarray import DocumentArray
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils

conf = ConfigUtils.load('conf/config.yaml')


class UpdateCenterNodeExecutor(YfExecutor):

    graph_mapper = GraphMapper(conf.get('NEO4J_CONFIGS'))

    @requests(on='/update_center_node')
    async def update_center_node(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags['kg_id']
            type_ = doc.tags.get('type')
            self.logger.info(f'{kg_id=} {type_=}')
            await sync_to_async(self.process)(int(kg_id), type_)
            self.logger.info('更新完成！！！！')

    @close_old_database_connections
    def process(self, kg_id: int, type_: str = ''):
        show_ontology = False
        if type_ == 'ontology':
            show_ontology = True
        self.logger.info(f'show_ontology: {show_ontology}')
        self.logger.info(f'kg_id: {kg_id}')
        label = GraphMapperUtil.get_label(kg_id)
        version = conf['version']
        labels = [label, version]
        ontology_label = GraphMapperUtil.get_ontology_label(kg_id)
        nodes = self.graph_mapper.get_max_relation_node_with_labels(
            labels, ontology_label=ontology_label, show_ontology=show_ontology)
        kg = Kg.objects.filter(id=kg_id).first()
        self.logger.info(f'nodes: {nodes}')
        hot_entities = []
        if nodes:
            eid = nodes[0].properties.get('_eid')
            self.logger.info(f'eid: {eid}')
            kg = Kg.objects.filter(id=kg_id).first()
            kg.center_node_eid = eid
            for node in nodes:
                name = node.properties.get('name')
                eid = node.properties.get('_eid')
                hot_entities.append({'id': eid, 'name': name, 'eid': eid})
            kg.hot_entities = hot_entities
            kg.save()
        self.logger.info('-------------------------------')
