from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils

from backend.graph import get_graph_mapper
from backend.models import Kg
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.utils import GraphMapperUtil

conf = ConfigUtils.load('conf/config.yaml')


class UpdateCenterNodeExecutor(YfExecutor):

    @requests(on='/update_center_node')
    async def update_center_node(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags['kg_id']
            type_ = doc.tags.get('type')
            self.logger.info(f'{kg_id=} {type_=}')
            await sync_to_async(self.process)(int(kg_id), type_)
            self.logger.info('更新完成！！！！')

    @close_old_database_connections
    def process(self, kg_id: int, type_: str = ''):
        show_ontology = False
        if type_ == 'ontology':
            show_ontology = True
        self.logger.info(f'show_ontology: {show_ontology}')
        self.logger.info(f'kg_id: {kg_id}')
        space = GraphMapperUtil.get_label(kg_id)
        version = conf['version']
        kg = Kg.objects.get(id=kg_id)
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        nodes = graph_mapper.get_max_relation_node_with_labels(
            space, version=version, show_ontology=show_ontology)
        self.logger.info(f'nodes: {nodes}')
        hot_entities = []
        if len(nodes) <= 0:
            nodes = graph_mapper.get_single_node_with_labels(
                space, version=version, show_ontology=show_ontology)
        if len(nodes) > 0:
            center_node_eid = nodes[0].props.get('_eid')
            self.logger.info(f'设置图谱{kg_id}的中心节点为{center_node_eid}')
            kg.center_node_eid = center_node_eid
            for node in nodes:
                name = node.props.get('_show_name')
                eid = node.props.get('_eid')
                hot_entity = {'id': eid, 'name': name, 'eid': eid}
                self.logger.info(f'设置热门实体{hot_entity}')
                hot_entities.append(hot_entity)
            kg.hot_entities = hot_entities
            kg.save()
        else:
            self.logger.info(f'{kg_id} 图谱为空或未找到中心节点')
        self.logger.info('-------------------------------')
