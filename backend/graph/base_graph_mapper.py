from abc import ABC, abstractmethod
from typing import Union

from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb, Neo4jGraphDb
from yunfu.db.graph.models import Node


class BaseGraphMapper(ABC):
    graph_db: Union[Neo4jGraphDb, NebulaGraphDb]

    @abstractmethod
    def get_node_by_eid(self, space: str, eid: str) -> Node:
        raise NotImplementedError

    @abstractmethod
    def get_max_relation_node_with_labels(
        self, space: str, version: str, show_ontology: bool = True
    ):
        raise NotImplementedError

    @abstractmethod
    def get_single_node_with_labels(
        self, space: str, version: str, show_ontology: bool = True
    ):
        raise NotImplementedError

    @abstractmethod
    def count_kg(self, space: str):
        raise NotImplementedError

    @abstractmethod
    def count_ontology(self, space: str):
        raise NotImplementedError

    @abstractmethod
    def count_kg_entities(self, space: str):
        raise NotImplementedError

    @abstractmethod
    def count_kg_properties(self, space: str):
        raise NotImplementedError

    @abstractmethod
    def count_kg_ontologies(self, space: str):
        raise NotImplementedError

    @abstractmethod
    def count_ontology_properties(self, space: str):
        raise NotImplementedError

    @abstractmethod
    def get_relation_types(self, space: str, eid: str = ""):
        raise NotImplementedError

    @abstractmethod
    def get_ontologies(self, space: str):
        raise NotImplementedError
