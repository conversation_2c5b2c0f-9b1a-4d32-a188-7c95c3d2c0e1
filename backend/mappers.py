from typing import Dict

from yfflow import YfLogger
from yunfu.common import ConfigUtils, SingletonMeta
from yunfu.db.graph.deprecated.graph import Neo4jGraph
from yunfu.db.graph.deprecated.neo4j_client import GraphDatabase, Neo4jClient

logger = YfLogger(__name__)

conf = ConfigUtils.load('conf/config.yaml')


class GraphMapper(Neo4jClient):
    __metaclass__ = SingletonMeta

    def __init__(self, config: Dict, space: str = 'default', use_version: bool = True) -> None:
        uri = f"{config['db']['schema']}://{config['db']['host']}:{config['db']['port']}"
        auth = (config['db']['username'], config['db']['password'])
        self.graph = GraphDatabase.driver(uri, auth=auth)
        self.min_count = config.get('min_count', 5)
        config = config['db']
        self.neo4j_graph = Neo4jGraph(config, space, use_version)

    def count_kg_properties(self, label: str):
        """统计图谱所有节点的属性总数"""
        query = (
            f'MATCH(n:c:`{label}`) WHERE NOT (n:c:`concept`) UNWIND KEYS(n) AS key WITH key'
            " WHERE NOT key STARTS WITH '_'"
            " AND NOT key CONTAINS '@'"
            " RETURN key, count(*)"
        )
        return self.run(query)

    def count_ontology_properties(self, label: str):
        """统计图谱所有本体的属性总数"""
        query = (
            f"MATCH(n:c:concept:`{label}`) WHERE n.name<>'事物' UNWIND KEYS(n) AS key WITH key"
            " WHERE NOT key IN ['_eid', '_show_name', '_create_time', '_update_time', '_type']"
            " RETURN key, count(*)"
        )
        return self.run(query)

    def count_kg_entities(self, label: str):
        """统计图谱所有实体的总数"""
        query = f'''MATCH(n:c:`{label}`) where not (n:`concept`) return count(n) as count'''
        result = self.run(query)
        return result[0]['count'] if result else 0

    def count_kg_ontologies(self, label: str):
        """统计图谱所有本体的总数"""
        query = f'''MATCH(n:c:`{label}`:`concept`) where n.name <> '事物' return count(n) as count'''
        result = self.run(query)
        return result[0]['count'] if result else 0

    def count_kg_relations(self, label: str):
        """统计图谱所有关系的总数"""
        query = '''MATCH(n:`{0}`)-[r]->(m:`{0}`) return count(r) as count'''.format(label)
        result = self.run(query)
        return result[0]['count'] if result else 0

    def count_kg(self, label: str):
        count_total = {
            'relations': 0,
            # 'ontologies': self.count_kg_ontologies(label),
            'properties': 0
        }
        results: Dict[str, dict] = {'entities': {}, 'relations': {}, 'properties': {}}
        query = (
            '''MATCH (n:c:`{0}`)-[r]->(m:c:`{0}`) WHERE NOT (n:concept) AND r.c=true '''
            '''RETURN DISTINCT(r.name) AS name, COUNT(r.name) AS count'''
        ).format(label)
        logger.info(f'first query: {query}')
        data = self.run(query)
        if data:
            logger.info(f'first query result: {data}')
            for item in data:
                results['relations'][item['name']] = item['count']
                count_total['relations'] += item['count']
        label_category = 'concept'
        query = (
            f'MATCH (n:c:{label})-[:`属于`*1..5]->(m:c:{label}:{label_category})-'
            f'[:`属于`]->(k:c:{label}:{label_category}) WHERE n.name <> m.name '
            f'AND not n:{label_category}  AND m.name <> "事物"'
            'RETURN DISTINCT m.name AS name, COUNT(DISTINCT n._eid) AS count, id(m) as id;'
        )
        data = self.run(query)
        if not data:
            query = (
                f'MATCH(n:c:`{label}`) where not (n:c:`concept`) '
                'RETURN DISTINCT(n._type) AS name, COUNT(n._type) AS count'
            )
            data = self.run(query)
        entity_count = self.count_kg_entities(label)
        count_total['entities'] = entity_count
        if data:
            count = 0
            for item in data:
                results['entities'][item['name']] = item['count']
                count += item['count']
            if count < entity_count:
                results['entities']['其他'] = entity_count - count
        properties_count = self.count_kg_properties(label)
        for item in properties_count:
            count_total['properties'] += item['count(*)']
            results['properties'][item['key']] = item['count(*)']
        return results, count_total

    def count_ontology(self, label: str):
        count_total = {
            'relations': 0,
            'properties': 0
        }
        results: Dict[str, dict] = {'entities': {}, 'relations': {}, 'properties': {}}
        query = (
            '''MATCH (n:concept:c:`{0}`)-[r]->(m:concept:c:`{0}`) WHERE m.name <> '事物' AND r.c=true '''
            '''RETURN DISTINCT(r.name) AS name, COUNT(r.name) AS count'''
        ).format(label)
        logger.info(f'first query: {query}')
        data = self.run(query)
        if data:
            logger.info(f'first query result: {data}')
            for item in data:
                results['relations'][item['name']] = item['count']
                count_total['relations'] += item['count']
        label_category = 'concept'
        query = (
            f'MATCH (n:concept:c:{label})-[:`属于`*1..5]->(m:concept:c:{label}:{label_category})-'
            f'[:`属于`]->(k:c:{label}:{label_category}) WHERE n.name <> m.name '
            f"AND not n:{label_category} "
            'RETURN DISTINCT m.name AS name, COUNT(DISTINCT n._eid) AS count, id(m) as id;'
        )

        data = self.run(query)
        if not data:
            query = (
                f"MATCH(n:c:concept:`{label}`) where n.name<>'事物' "
                "RETURN DISTINCT(n._type) AS name, COUNT(n._type) AS count"
            )
            data = self.run(query)
        entity_count = self.count_kg_ontologies(label)
        count_total['entities'] = entity_count
        if data:
            count = 0
            for item in data:
                results['entities'][item['name']] = item['count']
                count += item['count']
            if count < entity_count:
                results['entities']['其他'] = entity_count - count
        properties_count = self.count_ontology_properties(label)
        for item in properties_count:
            count_total['properties'] += item['count(*)']
            results['properties'][item['key']] = item['count(*)']
        return results, count_total

    def get_relation_types(self, label: str, eid: str = ''):
        match_query = f'MATCH (n:{label})-[r]-(m:{label})'
        where_query = "WHERE r.c=true"
        if eid:
            where_query = f"{where_query} AND n._eid='{eid}'"
        return_query = 'RETURN DISTINCT r.name'
        query = f'{match_query} {where_query} {return_query}'
        logger.info(f'query: {query}')
        results = self.run(query)
        return [result[0] for result in results]

    def get_ontologies(self, label: str):
        filter_root_query = ''
        if not conf['SHOW_ROOT']:
            filter_root_query = ' AND n.name<>"事物" '
        query = f'''
            MATCH (n) where n:{':'.join([label, 'concept', conf['version']])}{filter_root_query} return n.name
        '''
        results = self.run(query)
        return [result[0] for result in results]

    def get_max_relation_node_with_labels(self, labels: list, ontology_label: str = '', show_ontology: bool = True):
        # print("[GET MAX RELATION NODE WITH LABELS] labels:", labels)
        if show_ontology:
            query = f"""MATCH (n:{':'.join(labels)})-[r]-(m:{':'.join(labels)}) where n:concept
            AND n.name<>"事物"
            RETURN n, count(r) as rel_count ORDER BY rel_count DESC LIMIT 10
            """
        else:
            query = f"""MATCH (n:{':'.join(labels)})-[r]-(m:{':'.join(labels)}) where not n:concept
            AND n.name<>"事物"
            RETURN n, count(r) as rel_count ORDER BY rel_count DESC LIMIT 10
            """
        results = self.run(query)
        # print(f'match: {results}')
        return [(self._parse_node(result[0])) for result in results]

    def get_single_node_with_labels(self, labels: list, show_ontology: bool = True):
        # print("[GET MAX RELATION NODE WITH LABELS] labels:", labels)
        if show_ontology:
            query = f"""MATCH (n:{':'.join(labels)}) where n:concept RETURN n LIMIT 1"""
        else:
            query = f"""MATCH (n:{':'.join(labels)}) where not n:concept RETURN n LIMIT 1"""
        results = self.run(query)
        # print(f'match: {results}')
        return [(self._parse_node(result[0])) for result in results]

    def get_node_by_eid(self, label: str, eid: str):
        nodes = self.neo4j_graph.nodes.match(label, _eid=eid).all()
        if len(nodes) == 0:
            raise ValueError('实体不存在')
        return nodes[0]
