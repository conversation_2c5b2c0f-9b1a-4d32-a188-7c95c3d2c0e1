import os

import django
from django.conf import settings
from django.db import models
from django.db.models import Max
from yunfu.common import ConfigUtils

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()
_conf = ConfigUtils.load("conf/config.yaml")


class GraphDbs(models.TextChoices):
    NEO4J = "neo4j"
    NEBULA = "nebula"


class YfModel(models.Model):
    """基础model类"""

    created_at = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name="修改时间", auto_now=True)

    class Meta:
        abstract = True
        ordering = ["id"]


class Team(YfModel):
    """用户组"""

    name = models.CharField(max_length=255, verbose_name="组名")
    conf = models.JSONField(verbose_name="组配置", default=dict)
    parent = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, default=None
    )
    description = models.CharField(
        max_length=255, verbose_name="描述", blank=True, null=True, default=""
    )
    is_activate = models.BooleanField(verbose_name="启用状态", default=True)


class Kg(YfModel):

    class KgRanges(models.IntegerChoices):
        PRIVATE = 1  # 私有
        GROUP_OPENNESS = 2  # 组内公开
        TOTAL_OPENNESS = 3  # 完全公开

    class TypeChoices(models.TextChoices):
        ONTOLOGY = "ontology"  # 模版本体
        KG = "kg"  # 图谱
        UPDATE = "update"  # 更新用图谱

    name = models.CharField("名称", max_length=255)
    description = models.TextField(verbose_name="简介", null=True, blank=True)
    ontology_num = models.IntegerField(verbose_name="本体计数", default=0)
    property_num = models.IntegerField(verbose_name="属性计数", default=0)
    relation_num = models.IntegerField(verbose_name="关系计数", default=0)
    status = models.SmallIntegerField(verbose_name="状态", default=0)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="操作用户"
    )
    type = models.CharField(
        "图谱类型", choices=TypeChoices.choices, default=TypeChoices.KG, max_length=50
    )
    team = models.ForeignKey(
        Team, verbose_name="用户组", on_delete=models.CASCADE, blank=True, null=True
    )
    range = models.IntegerField(
        verbose_name="可见范围", choices=KgRanges.choices, blank=True, default=1
    )
    parent = models.ForeignKey(
        "self",
        verbose_name="裁剪来源",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        db_column="pid",
        related_name="children",
    )
    center_node_eid = models.CharField(
        verbose_name="中心节点eid", max_length=255, null=True, blank=True, default=None
    )
    hot_entities = models.JSONField(
        verbose_name="热门实体", null=True, blank=True, default=[]
    )
    db = models.CharField(
        verbose_name="图数据库", max_length=20, default=GraphDbs.NEO4J
    )

    @property
    def db_config(self) -> dict:
        return _conf["NEO4J_CONFIGS"]["db"] if self.db == GraphDbs.NEO4J else _conf["nebula"]  # type: ignore

    class Meta:
        db_table = "yunfu_due_kg_kg"
        verbose_name = "kg"
        ordering = ["-updated_at"]


class BaseCount(YfModel):
    """计数表"""

    kg = models.ForeignKey(Kg, on_delete=models.CASCADE, verbose_name="对应的图谱")
    name = models.CharField("名字", max_length=500)
    count = models.IntegerField(verbose_name="计数", default=0)

    class Meta:
        db_table = "yunfu_due_kg_basecount"
        ordering = ["-count"]
        verbose_name = "计数表"


class KgVersion(YfModel):
    """图谱版本表"""

    kg = models.ForeignKey(
        Kg, related_name="versions", on_delete=models.CASCADE, verbose_name="对应的图谱"
    )
    name = models.CharField(verbose_name="版本名称", max_length=255)
    number = models.IntegerField(verbose_name="版本号", default=0)
    description = models.TextField(verbose_name="简介", null=True, blank=True)
    entities = models.ManyToManyField(BaseCount, related_name="kg_count_entities")
    relations = models.ManyToManyField(BaseCount, related_name="kg_count_relations")
    properties = models.ManyToManyField(BaseCount, related_name="kg_count_properties")
    entity_count = models.IntegerField(verbose_name="实体数量", default=0)
    property_count = models.IntegerField(verbose_name="属性数量", default=0)
    relation_count = models.IntegerField(verbose_name="关系数量", default=0)
    center_node_eid = models.CharField(
        verbose_name="中心节点eid", max_length=255, null=True, blank=True, default=None
    )
    hot_entities = models.JSONField(
        verbose_name="热门实体", null=True, blank=True, default=[]
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="创建用户"
    )

    class Meta:
        db_table = "yunfu_due_kg_kgversion"
        verbose_name = "kg_version"
        ordering = ["-created_at"]

    @classmethod
    def get_next_number(cls, kg_id: int) -> int:
        number = KgVersion.objects.filter(kg__id=kg_id).aggregate(Max("number"))[
            "number__max"
        ]
        return (number or 0) + 1

    @classmethod
    def number2str(cls, number: int) -> str:
        if number == 0:
            return "c"
        return f"v{number}"


class KgRelationTypes(YfModel):
    """图谱关系类型表"""

    kg_id = models.IntegerField(
        verbose_name="图谱id", null=True, blank=True, unique=True
    )
    relation_types = models.JSONField(
        verbose_name="关系类型", default=dict, null=True, blank=True
    )

    class Meta:
        db_table = "yunfu_due_kg_kgrelationtypes"
        ordering = ["-updated_at"]
        verbose_name = "图谱关系类型表"


class KgOntologies(YfModel):
    """图谱本体表"""

    kg_id = models.IntegerField(
        verbose_name="图谱id", null=True, blank=True, unique=True
    )
    ontologies = models.JSONField(
        verbose_name="本体", default=dict, null=True, blank=True
    )

    class Meta:
        db_table = "yunfu_due_kg_kgontologies"
        ordering = ["-updated_at"]
        verbose_name = "图谱本体表"
