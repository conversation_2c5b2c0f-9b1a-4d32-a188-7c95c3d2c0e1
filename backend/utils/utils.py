from yunfu.common import ConfigUtils

conf = ConfigUtils.load('conf/config.yaml')


class GraphMapperUtil:

    @staticmethod
    def get_kg_prefix() -> str:
        return 'KG' if conf.get('IS_ONLINE_ENVIRONMENT') else 'KGT'

    @classmethod
    def get_label(cls, kg_id: int, project: str = '') -> str:
        """
        生成图数据节点标签
        区分线上环境(yunfu_due_kg)和本地开发环境(yunfu_due_kgT)
        :param kg_id: 图谱id
        """
        if project == 'ht12':
            return project
        prefix = cls.get_kg_prefix()
        return f'{prefix}{kg_id}'

    @classmethod
    def get_ontology_label(cls, kg_id: int) -> str:
        """
        生成图数据本体节点标签
        区分线上环境(yunfu_due_kg)和本地开发环境(yunfu_due_kgT)
        :param kg_id: 图谱id
        """
        return 'concept'
