NEO4J_CONFIGS:
  db:
    schema: bolt
    host: !env ${GRAPH_HOST|yftool-db-neo4j}
    port: !env ${GRAPH_PORT|7687}
    username: !env ${GRAPH_USERNAME|neo4j}
    password: !env ${GRAPH_PASSWORD|yunfu2017}
nebula:
  host: !env ${NEBULA_HOST|*************}
  port: !env ${NEBULA_PORT|9669}
  username: !env ${NEBULA_USERNAME|root}
  password: !env ${NEBULA_PASSWORD|root}
IS_ONLINE_ENVIRONMENT: true
version: c
SHOW_ROOT: false
