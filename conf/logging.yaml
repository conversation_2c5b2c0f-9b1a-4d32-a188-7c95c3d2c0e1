version: 1
disable_existing_loggers: False
formatters:
  verbose:
    format: "[%(asctime)s] [%(levelname)s] [%(process)d] [%(name)s:%(lineno)s] %(message)s"
handlers:
  console:
    level: INFO
    class: rich.logging.RichHandler
    formatter: verbose
    # rich_tracebacks: True
    show_time: False
  file:
    level: INFO
    class: logging.handlers.TimedRotatingFileHandler
    filename: logs/app.log
    when: D
    interval: 1
    backupCount: 30
    formatter: verbose
loggers:
  "":
    level: DEBUG
    handlers: ["console", "file"]
    propagate: False
