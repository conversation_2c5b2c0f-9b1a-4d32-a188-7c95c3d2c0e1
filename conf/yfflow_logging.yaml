handlers:  # enabled handlers, order does not matter
  - StreamHandler
  - logging.handlers.TimedRotatingFileHandler
level: INFO  # set verbose level
formatters:
  verbose:
    format: '[%(asctime)s] [%(levelname)s] [%(process)d] [%(name)s:%(lineno)s] %(message)s'
configs:
  logging.handlers.TimedRotatingFileHandler:
    format: verbose
    filename: logs/app.log
    formatter: PlainFormatter
    when: D
    interval: 1
    backupCount: 10
  rich.logging.RichHandler:
    format: verbose
    markup: false
    show_time: false
    rich_tracebacks: true
    show_path: false
    # log_time_format: '[%x %X]'
  FileHandler:
    format: verbose
    output: 'app.log'
    formatter: JsonFormatter
  StreamHandler:
    format: verbose
    formatter: PlainFormatter
