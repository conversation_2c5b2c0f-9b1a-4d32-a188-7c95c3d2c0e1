import asyncio

from yfflow import Client

texts = [
    {"text": "我叫汤姆去拿外衣1"},
    {"text": "我叫汤姆去拿外衣2"},
    {"text": "我叫汤姆去拿外衣3"},
    {"text": "我叫汤姆去拿外衣4"},
    {"text": "我叫汤姆去拿外衣5"},
    {"text": "我叫汤姆去拿外衣6"},
]
test2 = {
    "text": "我叫汤姆去拿外衣7"
}
# HTTP/GRPC/SOCKET
client = Client('yfproduct-yfkm-services-kg-count', 8000, 'GRPC', True)


async def get_res():
    # 批量请求，返回列表类型
    res = await client.post('/predict', texts, 2)
    print(res)
    # 单个请求，返回字典类型
    res = await client.post('/predict', test2)
    print(res)


asyncio.run(get_res())
