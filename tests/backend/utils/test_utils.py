import types

from backend.models import KgVersion
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.utils import GraphMapperUtil


def test_utils():
    assert GraphMapperUtil.get_kg_prefix() == 'KG'
    kg_id = 1
    project = 'test'
    assert GraphMapperUtil.get_label(kg_id, project) == 'KG1'
    project = 'ht12'
    assert GraphMapperUtil.get_label(kg_id, project) == 'ht12'
    assert GraphMapperUtil.get_ontology_label(kg_id) == 'concept'

# @close_old_database_connections
# def test():
#     kg_count = KgVersion.objects.filter(kg__id=1).first()
#     return '测试'
# res = close_old_database_connections(test)
# assert isinstance(res, types.FunctionType)
