from typing import Any, Dict, List

from py2neo.cypher import <PERSON><PERSON><PERSON>
from py2neo.data import Node as NeoN<PERSON>
from py2neo.data import Relationship as NeoEdge

from yunfu.db.graph.models import Edge, Node


class Py2neoDataWrapper:

    @staticmethod
    def as_rows(result: Cursor) -> List:
        """转换为行"""
        return [list(_.values()) for _ in result]

    @classmethod
    def as_dict(cls, result: Cursor) -> Dict[str, Any]:
        """转换为字典"""
        return result.data()[0]

    @classmethod
    def as_spaces(cls, result: Cursor, output: str = "label") -> List[str]:
        """转换为空间列表"""
        return [_.get(output) for _ in result]

    @classmethod
    def as_node_types(cls, result: Cursor, output: str = "label") -> List[str]:
        """转换为多个节点类型"""
        return [_.get(output) for _ in result]

    @classmethod
    def as_edge_types(cls, result: <PERSON>ursor, output: str = "type(r)") -> List[str]:
        """转换为多个边类型"""
        return [_.get(output) for _ in result]

    @classmethod
    def as_nodes(cls, result: Cursor, output: str = "n") -> List[Node]:
        return [cls.neo_node_to_node(_[output]) for _ in result.data()]

    @classmethod
    def as_node(cls, result: Cursor, output: str = "n") -> Node:
        return cls.neo_node_to_node(result.data()[0][output])

    @staticmethod
    def neo_node_to_node(neo_node: NeoNode) -> Node:
        labels = list(neo_node.labels)
        return Node(
            id=neo_node.get("_id"),
            types=labels,
            props=dict(neo_node),
        )

    @classmethod
    def as_edges(cls, result: Cursor, output: str = "r") -> List[Edge]:
        return [cls.neo_edge_to_edge(_[output]) for _ in result.data()]

    @classmethod
    def as_edge(cls, result: Cursor, output: str = "r") -> Edge:
        return cls.neo_edge_to_edge(result.data()[0][output])

    @classmethod
    def neo_edge_to_edge(cls, neo_edge: NeoEdge) -> Edge:
        edge = Edge(
            src_id=neo_edge.start_node.get("_id"),
            dst_id=neo_edge.end_node.get("_id"),
            type=type(neo_edge).__name__,
            props=dict(neo_edge),
            src_node=cls.neo_node_to_node(neo_edge.start_node),
            dst_node=cls.neo_node_to_node(neo_edge.end_node),
        )
        edge.id = edge.props.get("_id")
        return edge
